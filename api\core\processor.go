package core

import (
	"encoding/json"
	"jackpot/net"
)

// GenericProcessor 泛型消息处理器实现
type GenericProcessor[TRequest any, TResponse any] struct {
	handler         RequestHandler[TRequest, TResponse]
	responseBuilder ResponseBuilder[TResponse]
	logger          Logger
	moduleName      string
}

// NewGenericProcessor 创建新的泛型处理器
func NewGenericProcessor[TRequest any, TResponse any](moduleName string) *GenericProcessor[TRequest, TResponse] {
	return &GenericProcessor[TRequest, TResponse]{
		moduleName: moduleName,
	}
}

// SetHandler 设置请求处理器
func (p *GenericProcessor[TRequest, TResponse]) SetHandler(handler RequestHandler[TRequest, TResponse]) {
	p.handler = handler
}

// SetResponseBuilder 设置响应构建器
func (p *GenericProcessor[TRequest, TResponse]) SetResponseBuilder(builder ResponseBuilder[TResponse]) {
	p.responseBuilder = builder
}

// SetLogger 设置日志器
func (p *GenericProcessor[TRequest, TResponse]) SetLogger(logger Logger) {
	p.logger = logger
}

// ProcessMessage 处理消息
func (p *GenericProcessor[TRequest, TResponse]) ProcessMessage(msgBody []byte, done chan net.RequestChannel) {
	defer close(done)

	if p.handler == nil {
		p.logError("handler not set")
		done <- p.buildErrorResponse(net.MSG_STATE_ERROR_LOGIC)
		return
	}

	if p.responseBuilder == nil {
		p.logError("response builder not set")
		done <- p.buildErrorResponse(net.MSG_STATE_ERROR_LOGIC)
		return
	}

	// 解析请求
	var req TRequest
	if err := json.Unmarshal(msgBody, &req); err != nil {
		p.logWarn("recv json decode error: %v", err)
		done <- p.buildErrorResponse(net.MSG_STATE_ERROR_JSON)
		return
	}

	// 验证请求
	if err := p.handler.Validate(req); err != nil {
		p.logWarn("request validation error: %v", err)
		done <- p.buildErrorResponse(net.MSG_STATE_ERROR_JSON)
		return
	}

	// 处理请求
	jackpot, response, err := p.handler.Handle(req)
	if err != nil {
		p.logWarn("request handling error: %v", err)
		done <- p.buildErrorResponse(net.MSG_STATE_ERROR_LOGIC)
		return
	}

	// 构建成功响应
	done <- p.responseBuilder.BuildSuccess(response, jackpot)
}

// buildErrorResponse 构建错误响应
func (p *GenericProcessor[TRequest, TResponse]) buildErrorResponse(code byte) net.RequestChannel {
	if p.responseBuilder != nil {
		return p.responseBuilder.BuildError(code)
	}
	return net.RequestChannel{Code: code}
}

// logDebug 记录调试日志
func (p *GenericProcessor[TRequest, TResponse]) logDebug(format string, args ...any) {
	if p.logger != nil {
		p.logger.Debug(p.moduleName, format, args...)
	}
}

// logInfo 记录信息日志
func (p *GenericProcessor[TRequest, TResponse]) logInfo(format string, args ...any) {
	if p.logger != nil {
		p.logger.Info(p.moduleName, format, args...)
	}
}

// logWarn 记录警告日志
func (p *GenericProcessor[TRequest, TResponse]) logWarn(format string, args ...any) {
	if p.logger != nil {
		p.logger.Warn(p.moduleName, format, args...)
	}
}

// logError 记录错误日志
func (p *GenericProcessor[TRequest, TResponse]) logError(format string, args ...any) {
	if p.logger != nil {
		p.logger.Error(p.moduleName, format, args...)
	}
}

// DefaultResponseBuilder 默认响应构建器
type DefaultResponseBuilder[TResponse any] struct{}

// NewDefaultResponseBuilder 创建默认响应构建器
func NewDefaultResponseBuilder[TResponse any]() *DefaultResponseBuilder[TResponse] {
	return &DefaultResponseBuilder[TResponse]{}
}

// BuildSuccess 构建成功响应
func (b *DefaultResponseBuilder[TResponse]) BuildSuccess(data TResponse, jackpot int64) net.RequestChannel {
	responseData, err := json.Marshal(data)
	if err != nil {
		return net.RequestChannel{Code: net.MSG_STATE_ERROR_LOGIC}
	}

	return net.RequestChannel{
		Code:    net.MSG_STATE_OK,
		Data:    responseData,
		Jackpot: jackpot,
	}
}

// BuildError 构建错误响应
func (b *DefaultResponseBuilder[TResponse]) BuildError(code byte) net.RequestChannel {
	return net.RequestChannel{Code: code}
}
