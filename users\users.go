package users

import (
	"fmt"
	"jackpot/db"
	"jackpot/env"
	"jackpot/filelog"

	"gopkg.in/ini.v1"
)

// Stats 结构体定义
type Stats struct {
	Key             string           `json:"-"`
	UserId          string           `json:"uid"`
	Rtp             float64          `json:"rtp"` // 用户期望返奖率
	Cost            int64            `json:"-"`
	Repeat          int64            `json:"-"`
	FilterRand      float64          `json:"-"`
	FixedCost       int64            `json:"-"`
	FixedRepeat     int64            `json:"-"`
	FixedMulti      int64            `json:"-"`
	TotalCost       int64            `json:"totalCost"`       // 用户总成本
	TotalReturn     int64            `json:"totalReturn"`     // 用户总返奖
	TotalReturnRate float64          `json:"totalReturnRate"` // 实时返奖率
	Jy              map[int64]int64  `json:"jy"`              // 精英怪1成本
	CostMap         map[string]int64 `json:"costMap"`         // 用户成本分布
}

type GlobalStats struct {
	Key             string  `json:"-"`
	PlatId          string  `json:"id"`
	Ver             string  `json:"ver"`
	Rtp             float64 `json:"rtp"`
	TotalJackpot    int64   `json:"totalJackpot"`
	TotalCost       int64   `json:"totalCost"`
	TotalReturn     int64   `json:"totalReturn"`
	TotalReturnRate float64 `json:"totalReturnRate"`
	NeidanRtp       float64 `json:"neidanRtp"`
	NeidanJackpot   int64   `json:"neidanJackpot"`
}

const (
	KEYS_PLAT_STATS = "JACKPOT_STATS:%s:%s:PLAT:%d" // 平台奖池
	KEYS_USER_STATS = "JACKPOT_STATS:%s:%s:USER:%s" // 用户奖池

	VER_V2     = "v2"     // 宝物回收
	VER_ELIXIR = "elixir" // 仙丹模式
)

var (
	logger *filelog.FileLogger

	rtp       float64
	neidanRtp float64
)

func InitUsers(l *filelog.FileLogger, cfg *ini.File) error {
	logger = l

	var err error
	rtp, err = cfg.Section("users").Key("rtp").Float64()
	if err != nil {
		return err
	}
	neidanRtp, err = cfg.Section("users").Key("neidan").Float64()
	if err != nil {
		return err
	}
	backupDir = cfg.Section("log").Key("backup_dir").String()

	return nil
}

func GetUserStats(uid string, ver string) *Stats {
	// 从Redis获取Stats数据
	key := getUserKey(uid, ver)
	stats, err := db.Get[Stats](key)
	if err != nil {
		logger.Warn(uid, "%s", err.Error())
		return nil
	}
	if stats == nil {
		stats = &Stats{
			UserId:    uid,
			TotalCost: 1,
			Rtp:       rtp, // 用户最终期望返奖率
		}
	}
	stats.Key = key
	if stats.Jy == nil {
		stats.Jy = make(map[int64]int64)
	}
	if stats.CostMap == nil {
		stats.CostMap = make(map[string]int64)
	}
	return stats
}

func GetGlobalStats(cost int64, ver string) *GlobalStats {
	key := getGobalKey(cost, ver)
	globalStats, err := db.Get[GlobalStats](key)
	if err != nil {
		logger.Warn(key, "%s", err.Error())
		return nil
	}
	if globalStats == nil {
		globalStats = &GlobalStats{
			PlatId:    env.PlatId,
			Rtp:       rtp,
			NeidanRtp: neidanRtp,
			TotalCost: 1,
		}
	}
	globalStats.Key = key
	globalStats.Ver = ver
	return globalStats
}

func UpdateStats(stats *Stats) {
	stats.TotalReturnRate = float64(stats.TotalReturn) / float64(stats.TotalCost)
	if err := db.Set(stats.Key, stats); err != nil {
		logger.Warn(stats.UserId, "%s", err.Error())
	}
}

func UpdateGobalStats(global *GlobalStats) {
	global.TotalReturnRate = float64(global.TotalReturn) / float64(global.TotalCost)
	if err := db.Set(global.Key, global); err != nil {
		logger.Warn(global.PlatId, "%s", err.Error())
	}
}

func GetStatsMore(keys []string) ([]*Stats, error) {
	return db.Pipeline[Stats](keys)
}

func getGobalKey(cost int64, ver string) string {
	// currentTime := time.Now()
	// formattedDate := currentTime.Format("2006-01-02")
	return fmt.Sprintf(KEYS_PLAT_STATS, env.PlatId, ver, cost)
}

func getUserKey(uid string, ver string) string {
	// currentTime := time.Now()
	// formattedDate := currentTime.Format("2006-01-02")
	return fmt.Sprintf(KEYS_USER_STATS, env.PlatId, ver, uid)
}
