package db

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/redis/go-redis/v9"
)

var (
	rdb *redis.Client
	ctx context.Context
)

func Run(addr, password string, db int) error {
	rdb = redis.NewClient(&redis.Options{
		Addr:     addr,
		Password: password, // No password set
		DB:       db,       // Use default DB
		Protocol: 2,        // Connection protocol
	})

	ctx = context.Background()

	// 测试连接
	if _, err := rdb.Ping(ctx).Result(); err != nil {
		return err
	}
	return nil
}

func RunWithUrl(url string) error {
	opt, err := redis.ParseURL(url)
	if err != nil {
		return err
	}
	rdb = redis.NewClient(opt)
	ctx = context.Background()

	// 测试连接
	if _, err := rdb.Ping(ctx).Result(); err != nil {
		return err
	}
	return nil
}

// Get 获取键对应的值，并解析为Stats结构体
func Get[T any](key string) (*T, error) {
	val, err := rdb.Get(ctx, key).Result()
	if err != nil && err != redis.Nil {
		return nil, err
	}

	if err == redis.Nil {
		return nil, nil
	}

	var stats T
	err = json.Unmarshal([]byte(val), &stats)
	if err != nil {
		return nil, err
	}
	return &stats, nil
}

// Set 保存Stats结构体到Redis
func Set(key string, stats any) error {
	// 将Stats结构体序列化为JSON字符串
	data, err := json.Marshal(stats)
	if err != nil {
		return err
	}

	// 将JSON字符串保存到Redis
	err = rdb.Set(ctx, key, data, 0).Err() // 设置过期时间为0，即不过期
	return err
}

func Pipeline[T any](keys []string) ([]*T, error) {
	pipeline := rdb.Pipeline()

	// 使用MGet将多个Get命令添加到Pipeline中
	pipeline.MGet(ctx, keys...)

	// 执行Pipeline中的所有命令
	cmds, err := pipeline.Exec(ctx)
	if err != nil {
		return nil, err
	}

	// 处理返回结果
	var result []*T
	for _, cmd := range cmds {
		switch c := cmd.(type) {
		case *redis.SliceCmd:
			// 解析每个key的值
			for _, val := range c.Val() {
				var stats T
				err = json.Unmarshal([]byte(val.(string)), &stats)
				if err != nil {
					return nil, err
				}
				result = append(result, &stats)
			}
		default:
			return nil, fmt.Errorf("unknown command result: %v", c)
		}
	}

	return result, nil
}

func Rdb() *redis.Client {
	return rdb
}
