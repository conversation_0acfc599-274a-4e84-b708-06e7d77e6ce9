package filelog

import (
	"fmt"
	"jackpot/env"
	"log/slog"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/fatih/color"
)

type LogLevel int

const (
	DEBUG LogLevel = iota
	INFO
	WARN
	ERROR
)

type LogEntry struct {
	UserId  string         `json:"uid"`
	Level   LogLevel       `json:"lv"`
	Message string         `json:"message"`
	Body    map[string]any `json:"body"`
}

type FileLogger struct {
	dir      string
	pattern  string
	seq      int
	mu       sync.Mutex
	logFile  *os.File
	logWr    *slog.Logger
	nextTime time.Time
	nextSize int
	logChan  chan LogEntry
}

var (
	red    = color.New(color.FgRed).SprintfFunc()
	yellow = color.New(color.FgYellow).SprintfFunc()
	gray   = color.RGB(100, 100, 100).SprintfFunc()
	logLv  slog.Level
)

func NewFileLogger(dir string, pattern string, lv string, nextTime int, nextSize int) (*FileLogger, error) {
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, err
	}

	logger := &FileLogger{
		dir:      dir,
		pattern:  pattern,
		seq:      1,
		nextTime: time.Now().Add(time.Duration(nextTime) * time.Hour), // 每天滚动
		nextSize: nextSize * 1024 * 1024,
		logChan:  make(chan LogEntry, 10000),
	}

	switch lv {
	case "debug":
		logLv = slog.LevelDebug
	case "info":
		logLv = slog.LevelInfo
	case "warn":
		logLv = slog.LevelWarn
	case "error":
		logLv = slog.LevelError
	default:
		logLv = slog.LevelWarn
	}

	if err := logger.createLogFile(); err != nil {
		return nil, err
	}

	slog.SetLogLoggerLevel(logLv)

	go logger.run()
	return logger, nil
}

func (f *FileLogger) createLogFile() error {
	f.mu.Lock()
	defer f.mu.Unlock()

	if f.logFile != nil {
		f.logFile.Close()
	}

	timestamp := time.Now().Format(f.pattern)
	fileName := filepath.Join(f.dir, fmt.Sprintf("%s_%d.log", timestamp, f.seq))
	file, err := os.OpenFile(fileName, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return err
	}
	f.logFile = file
	// f.logWr = slog.New(slog.NewJSONHandler(file, nil))
	f.logWr = slog.New(slog.NewTextHandler(file, &slog.HandlerOptions{Level: logLv}))
	f.logWr = f.logWr.With("version", env.Version)
	f.seq++

	return nil
}

func (f *FileLogger) run() {
	for entry := range f.logChan {
		if f.needNewLogFile() {
			f.nextTime = time.Now().Add(24 * time.Hour)
			if err := f.createLogFile(); err != nil {
				panic(err)
			}
		}

		switch entry.Level {
		case DEBUG:
			f.debug(entry.UserId, entry.Message, entry.Body)
		case INFO:
			f.info(entry.UserId, entry.Message, entry.Body)
		case WARN:
			f.warn(entry.UserId, entry.Message, entry.Body)
		case ERROR:
			f.error(entry.UserId, entry.Message, entry.Body)
		}
	}
}

func (f *FileLogger) needNewLogFile() bool {
	if f.logFile == nil {
		return true
	}

	if time.Now().After(f.nextTime) {
		f.seq = 1
		return true
	}

	fi, err := f.logFile.Stat()
	if err != nil {
		slog.Error(fmt.Sprintf("Failed to stat log file: %v", err))
		return false
	}

	return fi.Size() >= int64(f.nextSize)
}

func (f *FileLogger) debug(uid string, msg string, body map[string]any) {
	var log1 *slog.Logger
	var log2 *slog.Logger
	if env.Debug {
		log1 = slog.With("uid", uid)
	}
	log2 = f.logWr.With("uid", uid)
	for k, v := range body {
		if log1 != nil {
			log1 = log1.With(k, v)
		}
		if log2 != nil {
			log2 = log2.With(k, v)
		}
	}
	if log1 != nil {
		log1.Debug(gray(msg))
	}
	if log2 != nil {
		log2.Debug(msg)
	}
}

func (f *FileLogger) info(uid string, msg string, body map[string]any) {
	var log1 *slog.Logger
	var log2 *slog.Logger
	if env.Debug {
		log1 = slog.With("uid", uid)
	}
	log2 = f.logWr.With("uid", uid)
	for k, v := range body {
		if log1 != nil {
			log1 = log1.With(k, v)
		}
		if log2 != nil {
			log2 = log2.With(k, v)
		}
	}
	if log1 != nil {
		log1.Info(msg)
	}
	if log2 != nil {
		log2.Info(msg)
	}
}

func (f *FileLogger) warn(uid string, msg string, body map[string]any) {
	var log1 *slog.Logger
	var log2 *slog.Logger
	log1 = slog.With("uid", uid)
	log2 = f.logWr.With("uid", uid)
	for k, v := range body {
		if log1 != nil {
			log1 = log1.With(k, v)
		}
		if log2 != nil {
			log2 = log2.With(k, v)
		}
	}
	if log1 != nil {
		log1.Warn(yellow(msg))
	}
	if log2 != nil {
		log2.Warn(msg)
	}
}

func (f *FileLogger) error(uid string, msg string, body map[string]any) {
	var log1 *slog.Logger
	var log2 *slog.Logger
	log1 = slog.With("uid", uid)
	log2 = f.logWr.With("uid", uid)
	for k, v := range body {
		if log1 != nil {
			log1 = log1.With(k, v)
		}
		if log2 != nil {
			log2 = log2.With(k, v)
		}
	}
	if log1 != nil {
		log1.Error(red(msg))
	}
	if log2 != nil {
		log2.Error(msg)
	}
}

func (f *FileLogger) Log(lv LogLevel, uid string, msg string, body map[string]any) {
	entry := LogEntry{
		UserId:  uid,
		Level:   lv,
		Message: msg,
	}
	if body != nil {
		entry.Body = body
	} else {
		entry.Body = make(map[string]any)
	}
	f.logChan <- entry // Send entry to the log channel
}

func (f *FileLogger) Debug(uid string, msg string, a ...any) {
	f.Log(DEBUG, uid, fmt.Sprintf(msg, a...), nil)
}

func (f *FileLogger) Info(uid string, msg string, a ...any) {
	f.Log(INFO, uid, fmt.Sprintf(msg, a...), nil)
}

func (f *FileLogger) Warn(uid string, msg string, a ...any) {
	f.Log(WARN, uid, fmt.Sprintf(msg, a...), nil)
}

func (f *FileLogger) Error(uid string, msg string, a ...any) {
	f.Log(ERROR, uid, fmt.Sprintf(msg, a...), nil)
}

func (f *FileLogger) Close() error {
	f.mu.Lock()
	defer f.mu.Unlock()

	if f.logFile != nil {
		close(f.logChan)
		return f.logFile.Close()
	}
	return nil
}

func (f *FileLogger) IsDebug() bool {
	return logLv == slog.LevelDebug
}
