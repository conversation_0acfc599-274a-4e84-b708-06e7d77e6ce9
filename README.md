# Jackpot Service

一个基于Go语言开发的高性能游戏抽奖服务，采用现代化的微服务架构设计，支持WebSocket实时通信和多种抽奖玩法。

## 🎯 项目概述

Jackpot Service是一个专业的游戏抽奖后端服务，提供完整的抽奖系统解决方案，包括：

- **多种抽奖模式**：怪物抽奖、每日福利、内丹系统、世界BOSS、物品兑换等
- **实时通信**：基于WebSocket的双向通信，支持实时消息推送
- **高性能架构**：采用泛型+接口+依赖注入的现代Go架构
- **数据持久化**：Redis数据库支持，高效的数据存储和缓存
- **统计分析**：完整的用户行为统计和全局数据分析
- **排行榜系统**：实时排行榜更新和查询功能

## 🏗️ 技术架构

### 核心技术栈

- **语言**: Go 1.23.2
- **Web框架**: Fiber v2 (高性能HTTP框架)
- **WebSocket**: Fiber WebSocket支持
- **数据库**: Redis (数据存储和缓存)
- **配置管理**: INI配置文件
- **日志系统**: 自定义文件日志系统

### 架构特点

- **模块化设计**: 每个功能模块独立开发和部署
- **泛型支持**: 利用Go 1.18+泛型特性，提高代码复用性
- **依赖注入**: 降低模块间耦合，提高可测试性
- **接口驱动**: 统一的接口规范，便于扩展和维护
- **类型安全**: 编译时类型检查，减少运行时错误

## 📦 API模块

### 核心模块列表

| 模块 | 版本 | 消息类型 | 描述 | 功能 |
|------|------|----------|------|------|
| monster | v2 | 1 | 怪物抽奖模块 | 普通抽奖，支持多种消费档位 |
| daily | v2 | 5 | 每日福利模块 | 每日签到奖励和福利发放 |
| stats | v1 | 4 | 统计模块 | 用户行为统计和数据分析 |
| boss | v1 | 3 | 世界BOSS模块 | 世界BOSS挑战和奖励 |
| exchange | v1 | 9 | 兑换模块 | 物品兑换和积分消费 |
| neidan | v4 | 11 | 内丹模块 | 内丹抽奖系统 |
| rank | v1 | 7 | 排行榜模块 | 排行榜数据更新 |
| rank/query | v1 | 8 | 排行榜查询模块 | 排行榜数据查询 |

### 消费档位

系统支持三种消费档位：
- **100档**: 低消费抽奖
- **1000档**: 中消费抽奖  
- **10000档**: 高消费抽奖

## 🚀 快速开始

### 环境要求

- Go 1.23.2 或更高版本
- Redis 服务器
- Windows/Linux/macOS

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd jackpot_service
```

2. **安装依赖**
```bash
go mod download
```

3. **配置Redis**
编辑 `config.ini` 文件中的Redis配置：
```ini
[redis]
addr = "127.0.0.1:6379"
password = ""
db = 0
```

4. **配置日志**
```ini
[log]
dir = "logs"
pattern = "20060102_15"
lv = "debug"
next_time = 1
next_size = 50
```

5. **编译运行**
```bash
# 编译
go build -o jackpot.exe .

# 运行
./jackpot.exe

# 或直接运行
go run main.go
```

### 命令行参数

- `-d`: 启用调试模式

```bash
./jackpot.exe -d  # 调试模式运行
```

## 🔧 配置说明

### 基础配置 (config.ini)

```ini
[base]
appid = 1                    # 应用ID
appname = 'jackpot_service'  # 应用名称
appver = '1.1.0'            # 版本号
platid = 991                # 平台ID
port = 30001                # 服务端口
timer = 60                  # 定时器间隔(分钟)

[users]
rtp = 0.95                  # 返奖率
neidan = 0.04               # 内丹概率
rankTax = 0                 # 排行榜税率
bossTax = 0                 # BOSS税率
bossRtp = 0                 # BOSS返奖率
```

## 📡 API接口

### WebSocket连接

```
ws://localhost:30001/ws/{platid}?v={version}&timeout={timeout}
```

参数说明：
- `platid`: 平台ID
- `version`: 客户端版本
- `timeout`: 超时时间(秒)

### HTTP接口

#### 1. 模块信息查询
```
GET /api/modules
GET /api/modules?admin=true
GET /api/modules?admin=true&detailed=true
```

**基础模式**：返回所有已注册模块的基本信息
**管理员模式**：添加 `admin=true` 参数获取系统详细信息
**详细模式**：添加 `detailed=true` 参数获取模块详细状态

**基础响应示例**：
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "modules": [
      {
        "name": "monster",
        "version": "v2",
        "messageType": 1
      }
    ],
    "supportedMessageTypes": [1, 5, 4, 3, 9, 11, 7, 8],
    "totalModules": 8
  }
}
```

**管理员模式响应示例**：
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "modules": [...],
    "supportedMessageTypes": [...],
    "totalModules": 8,
    "systemInfo": {
      "version": "1.1.0",
      "platform": "991",
      "architecture": "amd64",
      "debugMode": false,
      "uptime": "2h30m15s",
      "startTime": "2024-01-01 10:30:00"
    },
    "configuration": {
      "port": "30001",
      "timerInterval": "60",
      "logLevel": "debug",
      "redisAddr": "127.0.0.1:6379"
    },
    "runtime": {
      "activeConnections": 0,
      "processedMessages": 0,
      "errorCount": 0
    }
  }
}
```

**详细模式额外字段**：
```json
{
  "moduleDetails": [
    {
      "name": "monster",
      "version": "v2",
      "messageType": 1,
      "status": "active",
      "initialized": true,
      "processorStatus": "registered"
    }
  ]
}
```

#### 2. 全局统计查询
```
GET /stats/global/{cost}
```

查询指定消费档位的全局统计数据。

#### 3. 全局统计更新
```
POST /stats/global/{cost}/{key}
Content-Type: application/json

{
  "value": "新值"
}
```

#### 4. 数据清理
```
POST /stats/cleanup
```

清理过期的Redis数据。

## 🛠️ 开发指南

### 架构设计原则

本项目采用**泛型+接口+依赖注入**的现代化架构设计，具有以下特点：

1. **统一模块结构**: 所有API模块遵循统一的接口规范
2. **类型安全**: 利用Go泛型确保编译时类型检查
3. **代码复用**: 通过泛型减少重复代码
4. **依赖注入**: 降低模块间耦合度，提高可测试性
5. **接口驱动**: 清晰的职责分离，便于单元测试

### 核心组件说明

#### 1. 接口定义 (`api/core/interfaces.go`)
- `APIModule`: 定义API模块的基础接口
- `RequestHandler`: 泛型请求处理器接口
- `ResponseBuilder`: 响应构建器接口
- `Container`: 依赖注入容器接口
- `ModuleRegistry`: 模块注册器接口

#### 2. 泛型处理器 (`api/core/processor.go`)
- `GenericProcessor`: 泛型消息处理器，处理通用的消息流程
- `DefaultResponseBuilder`: 默认响应构建器

#### 3. 依赖注入容器 (`api/core/container.go`)
- `DIContainer`: 依赖注入容器实现
- `LoggerAdapter`: 日志器适配器

#### 4. 模块注册器 (`api/core/registry.go`)
- `DefaultModuleRegistry`: 模块注册器实现
- `ProcessorRegistry`: 处理器注册器

#### 5. 基础模块 (`api/core/base_module.go`)
- `BaseModule`: 基础模块实现，简化模块创建
- `ModuleBuilder`: 模块构建器，支持链式调用

### 创建新模块

#### 步骤1: 定义请求和响应结构

```go
// 请求结构体
type MyModuleRequest struct {
    UserId string `json:"uid"`
    Cost   int64  `json:"cost"`
    Param1 string `json:"param1"`
}

// 响应结构体
type MyModuleResponse struct {
    MyModuleRequest
    Items  []items.Item `json:"items"`
    Result string       `json:"result"`
}
```

#### 步骤2: 实现请求处理器

```go
type MyModuleHandler struct {
    logger core.Logger
    // 其他依赖...
}

// Init 初始化处理器
func (h *MyModuleHandler) Init(logger *filelog.FileLogger, config interface{}) error {
    h.logger = core.NewLoggerAdapter(logger)
    // 初始化其他依赖...
    return nil
}

// Validate 验证请求参数
func (h *MyModuleHandler) Validate(req MyModuleRequest) error {
    if req.UserId == "" {
        return fmt.Errorf("missing uid parameter")
    }

    // 验证Cost参数
    switch req.Cost {
    case net.COST_TYPE_100, net.COST_TYPE_1000, net.COST_TYPE_10000:
    default:
        return fmt.Errorf("invalid cost parameter")
    }

    return nil
}

// Handle 处理业务逻辑
func (h *MyModuleHandler) Handle(req MyModuleRequest) (int64, net.Response, error) {
    // 实现具体的业务逻辑
    jackpot := int64(0)

    response := net.Response{
        Code: 0,
        Msg:  "",
        Data: &MyModuleResponse{
            MyModuleRequest: req,
            Items:          []items.Item{},
            Result:         "success",
        },
    }

    return jackpot, response, nil
}
```

#### 步骤3: 创建模块构建函数

```go
// NewMyModuleHandler 创建处理器实例
func NewMyModuleHandler() *MyModuleHandler {
    return &MyModuleHandler{}
}

// NewMyModule 创建模块
func NewMyModule() core.APIModule {
    handler := NewMyModuleHandler()

    return core.NewModuleBuilder[MyModuleRequest, net.Response]("mymodule", "v1", net.MSG_TYPE_CUSTOM).
        WithHandler(handler).
        Build()
}
```

#### 步骤4: 注册模块

在 `api/manager.go` 的 `registerModules` 方法中添加新模块：

```go
func (m *Manager) registerModules() error {
    modules := []core.APIModule{
        monster.NewMonsterModule(),
        daily.NewDailyModule(),
        stats.NewStatsModule(),
        boss.NewBossModule(),
        exchange.NewExchangeModule(),
        neidan.NewNeidanModule(),
        rank.NewRankModule(),
        query.NewQueryModule(),
        mymodule.NewMyModule(), // 添加新模块
    }

    // 注册逻辑...
}
```

### 模块开发规范

#### 1. 文件结构
```
api/
├── mymodule/
│   └── module.go          # 包含所有模块代码
├── core/                  # 核心组件
└── manager.go            # 模块管理器
```

#### 2. 命名规范
- **模块名**: 小写，简洁明了 (如: `monster`, `daily`)
- **版本号**: 语义化版本 (如: `v1`, `v2.1`)
- **消息类型**: 使用 `net` 包中定义的常量
- **结构体**: 驼峰命名，以模块名开头

#### 3. 接口实现
每个模块必须实现以下接口：
- `RequestHandler[TRequest, TResponse]`: 请求处理接口
- `Initializer`: 初始化接口 (可选)

#### 4. 错误处理
- 使用标准的Go错误处理模式
- 验证错误返回具体的错误信息
- 业务逻辑错误通过响应码传递

#### 5. 日志记录
```go
h.logger.Debug("tag", "debug message: %v", data)
h.logger.Info("tag", "info message: %s", info)
h.logger.Warn("tag", "warning: %v", warning)
h.logger.Error("tag", "error occurred: %v", err)
```

### 消息类型定义

在 `net` 包中定义新的消息类型常量：

```go
const (
    MSG_TYPE_CUSTOM = 12  // 自定义消息类型
    // 添加更多消息类型...
)
```

### 架构优势

#### 1. 简化的main.go
**重构前的方式**：
```go
// 需要手动初始化每个模块
if err = monster.Init(logger); err != nil {
    panic(err)
}
if err = daily.Init(logger); err != nil {
    panic(err)
}
// ... 更多模块

// 硬编码的消息路由
switch msgType {
case net.MSG_TYPE_OPEN_COMMON:
    process = monster.Process
case net.MSG_TYPE_OPEN_DAILY:
    process = daily.Process
// ... 更多case
}
```

**现在的方式**：
```go
// 统一管理，自动发现和初始化
apiManager = api.NewManager()
if err = apiManager.Initialize(logger, cfg); err != nil {
    panic(err)
}

// 动态路由，自动处理
if !apiManager.ProcessMessage(msgType, msgBody, done) {
    // 处理未知消息类型
}
```

#### 2. 类型安全的泛型处理
**重构前**：每个模块都有重复的JSON处理代码
```go
var req RequestBody
if err := json.Unmarshal(msgBody, &req); err != nil {
    // 错误处理
}
```

**现在**：泛型处理器自动处理
```go
processor := NewGenericProcessor[RequestBody, ResponseData]("module")
processor.ProcessMessage(msgBody, done)
```

#### 3. 统一的错误处理和验证
- 自动JSON解析和验证
- 统一的错误响应格式
- 类型安全的参数验证

### 新增功能

#### 1. 模块信息API
访问 `GET /api/modules` 可以获取所有已注册模块的信息：

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "modules": [
      {
        "name": "monster",
        "version": "v2",
        "messageType": 1
      },
      {
        "name": "daily",
        "version": "v2",
        "messageType": 5
      }
    ],
    "supportedMessageTypes": [1, 5, 4, 3, 9, 11, 7, 8],
    "totalModules": 8
  }
}
```

#### 2. 运行时模块管理
支持在运行时动态注册新模块：

```go
customModule := NewCustomModule()
err := apiManager.RegisterCustomModule(customModule)
```

#### 3. 统一的依赖注入
```go
// 注册依赖
container.Register("database", dbInstance)
container.Register("cache", cacheInstance)

// 在模块中获取依赖
db := core.MustGetTyped[*Database](container, "database")
cache := core.MustGetTyped[*Cache](container, "cache")
```

## 🧪 开发和测试

### 运行测试

```bash
# 运行核心模块测试
go test -v ./api/core/...

# 运行所有测试
go test -v ./...
```

### 编译验证

```bash
# 编译检查
go build -o jackpot.exe .

# 交叉编译 (Linux)
GOOS=linux GOARCH=amd64 go build -o jackpot_linux .
```

### 调试模式

启用调试模式可以获得更详细的日志输出：

```bash
./jackpot.exe -d
```

### 模块测试

创建模块测试文件 `api/mymodule/module_test.go`：

```go
package mymodule

import (
    "testing"
    "jackpot/api/core"
    "jackpot/net"
)

func TestMyModuleHandler_Validate(t *testing.T) {
    handler := NewMyModuleHandler()

    // 测试有效请求
    validReq := MyModuleRequest{
        UserId: "test123",
        Cost:   net.COST_TYPE_100,
        Param1: "test",
    }

    if err := handler.Validate(validReq); err != nil {
        t.Errorf("Expected no error, got %v", err)
    }

    // 测试无效请求
    invalidReq := MyModuleRequest{
        UserId: "", // 缺少UserId
        Cost:   net.COST_TYPE_100,
    }

    if err := handler.Validate(invalidReq); err == nil {
        t.Error("Expected validation error for missing UserId")
    }
}
```

## 📊 监控和日志

### 日志系统

- **日志目录**: `logs/`
- **备份目录**: `backups/`
- **日志格式**: `20060102_15` (年月日_小时)
- **日志级别**: debug, info, warn, error
- **自动轮转**: 按时间和大小自动创建新日志文件

### 性能监控

系统提供以下监控指标：
- WebSocket连接数
- 消息处理延迟
- 模块处理统计
- Redis操作性能
- 内存使用情况

## 🔒 安全特性

- **消息去重**: 防止重复消息处理
- **超时控制**: 可配置的消息处理超时
- **参数验证**: 严格的输入参数验证
- **错误处理**: 完善的错误处理和恢复机制
- **日志审计**: 完整的操作日志记录

## 📈 性能优化

- **连接池**: Redis连接池管理
- **内存缓存**: 热点数据内存缓存
- **异步处理**: 非阻塞的消息处理
- **批量操作**: 支持批量数据操作
- **资源回收**: 自动资源清理和回收

---

**版本**: 1.1.0  
**最后更新**: 2024年  
**开发语言**: Go 1.23.2  
**架构**: 微服务 + WebSocket + Redis
