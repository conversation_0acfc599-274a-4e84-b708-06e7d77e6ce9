package unseal

import (
	"encoding/json"
	"fmt"
	"jackpot/api/core"
	"jackpot/filelog"
	"jackpot/items"
	"jackpot/net"
	"jackpot/users"
	"math/rand/v2"
	"os"
)

// Request 请求体
type Request struct {
	Oid    string  `json:"oid"`    // 批次ID
	UserId string  `json:"uid"`    // 用户ID，如果是全局开奖，此处传固定值 -1
	Map    int64   `json:"map"`    // 0-全部地图 100-火焰山，1000-盘丝洞，10000-狮驼岭
	Counts []int64 `json:"counts"` // 每种物品的数量，全地图时长度为24，否则为8
}

// Response 响应数据
type Response struct {
	Request
	Items []items.Item `json:"items"`
}

// Handler 处理器
type Handler struct {
	logger  core.Logger
	itemMap map[int64][]items.Item
	giftMap map[int64]map[int][]items.Item // 修改为支持不同地图：map[mapId][count][]items.Item
}

// NewHandler 创建处理器
func NewHandler() *Handler {
	return &Handler{
		itemMap: make(map[int64][]items.Item),
		giftMap: make(map[int64]map[int][]items.Item),
	}
}

// Init 初始化处理器
func (h *Handler) Init(logger *filelog.FileLogger, config any) error {
	h.logger = core.NewLoggerAdapter(logger)

	// 读取仙丹开鼎配置文件
	jsonData, err := os.ReadFile("elixir_unseal.json")
	if err != nil {
		return err
	}

	var giftMapTemp = make(map[string]map[string][]items.ItemConfig)
	err = json.Unmarshal(jsonData, &giftMapTemp)
	if err != nil {
		return err
	}

	// 将配置转换为内部格式
	for mapKey, countMap := range giftMapTemp {
		var mapId int64
		fmt.Sscanf(mapKey, "%d", &mapId)

		if h.giftMap[mapId] == nil {
			h.giftMap[mapId] = make(map[int][]items.Item)
		}

		for countKey, itemConfigs := range countMap {
			var count int
			fmt.Sscanf(countKey, "%d", &count)

			var itemList []items.Item
			for _, itemConfig := range itemConfigs {
				itemList = append(itemList, items.Item(itemConfig))
			}
			h.giftMap[mapId][count] = itemList
		}
	}

	h.logger.Debug("elixir_unseal", "giftMap: %+v", h.giftMap)
	return nil
}

// Validate 验证请求
func (h *Handler) Validate(req Request) error {
	if len(req.UserId) == 0 {
		return fmt.Errorf("无效用户参数")
	}

	if req.Map > 0 {
		switch req.Map {
		case net.COST_TYPE_100, net.COST_TYPE_1000, net.COST_TYPE_10000:
		default:
			return fmt.Errorf("无效地图参数")
		}

		if len(req.Counts) != 8 {
			return fmt.Errorf("无效或缺少counts参数")
		}
	} else if req.Map == 0 {
		if len(req.Counts) != 24 {
			return fmt.Errorf("无效或缺少counts参数")
		}
	} else {
		return fmt.Errorf("无效地图参数")
	}

	return nil
}

// Handle 处理请求
func (h *Handler) Handle(req Request) (int64, net.Response, error) {
	var jackpot int64
	var itemList []items.Item

	if req.Map > 0 {
		jackpot, itemList = h.open(req.Map, req.Counts)
	} else if req.Map == 0 {
		// 分割 req.Counts 为 3 个长度为 8 的切片
		var countSlices [3][]int64
		for i := range 3 {
			start := i * 8
			end := start + 8
			countSlices[i] = req.Counts[start:end]
		}

		// 定义一个包含3个地图枚举的数组
		mapId := [3]int64{net.COST_TYPE_100, net.COST_TYPE_1000, net.COST_TYPE_10000}

		// 定义一个空数组 results，数组类型是 items.Item
		results := make([]items.Item, 0)

		// 分别处理每个切片
		var jackpotResult int64
		for i, countSlice := range countSlices {
			var jp int64
			var items []items.Item
			// if req.UserId == "-1" && i > 1 {
			// 	jp, items = h.openGlobal(mapId[i], countSlice)
			// } else {
			// 	jp, items = h.open(mapId[i], countSlice)
			// }
			jp, items = h.open(mapId[i], countSlice)
			jackpotResult += jp
			results = append(results, items...)
		}

		jackpot = jackpotResult
		itemList = results
	}

	response := net.Response{
		Code: 0,
		Msg:  "",
		Data: &Response{
			Request: req,
			Items:   itemList,
		},
	}

	return jackpot, response, nil
}

// open 开奖逻辑
func (h *Handler) open(mapId int64, countList []int64) (int64, []items.Item) {
	// 获取全局奖池信息
	global := users.GetGlobalStats(mapId, users.VER_ELIXIR)

	h.logger.Debug("elixir_unseal", "[1] start open, mapId:%d, countList:%+v, global jackpot:%d", mapId, countList, global.TotalJackpot)

	// 步骤1: 随机选择4个仙丹（0-7，可重复）
	selectedElixirs := make([]int, 4)
	for i := 0; i < 4; i++ {
		selectedElixirs[i] = rand.IntN(8) // 0-7
	}

	h.logger.Debug("elixir_unseal", "[2] selected elixirs: %+v", selectedElixirs)

	// 步骤2: 统计每种仙丹的出现次数
	elixirCounts := make(map[int]int)
	for _, elixir := range selectedElixirs {
		elixirCounts[elixir]++
	}

	h.logger.Debug("elixir_unseal", "[3] elixir counts: %+v", elixirCounts)

	// 步骤3: 为每种出现的仙丹选择礼物
	selectedGifts := make(map[int]items.Item) // key: 仙丹索引, value: 选中的礼物
	for elixir, count := range elixirCounts {
		// 根据地图和出现次数从对应奖池中选择礼物
		mapGiftPool, exists := h.giftMap[mapId]
		if !exists {
			h.logger.Debug("elixir_unseal", "[4] no gift pool for map %d", mapId)
			continue
		}

		giftPool, exists := mapGiftPool[count]
		if !exists || len(giftPool) == 0 {
			h.logger.Debug("elixir_unseal", "[4] no gift pool for map %d count %d", mapId, count)
			continue
		}

		selectedGift := items.SelectItem("elixir_unseal", giftPool)
		selectedGifts[elixir] = selectedGift

		h.logger.Debug("elixir_unseal", "[4] elixir %d (count %d) selected gift: %s (value:%d)", elixir, count, selectedGift.Name, selectedGift.Value)
	}

	// 步骤4: 根据countList分配数量并计算总价值
	result := make([]items.Item, len(countList))
	totalValue := int64(0)

	for i, count := range countList {
		if count <= 0 {
			continue
		}

		elixirIndex := i % 8 // 获取对应的仙丹索引

		// 检查该仙丹是否出现过
		if gift, exists := selectedGifts[elixirIndex]; exists {
			// 仙丹出现过，使用选中的礼物
			result[i] = items.Item{
				ID:    gift.ID,
				Name:  gift.Name,
				Value: gift.Value,
				Count: gift.Count * count, // 根据countList中的数量倍增
				Total: count,
			}
			totalValue += gift.Value * gift.Count * count
		} else {
			// 仙丹未出现，使用出现次数0的奖池
			mapGiftPool, exists := h.giftMap[mapId]
			if exists {
				giftPool, exists := mapGiftPool[0]
				if exists && len(giftPool) > 0 {
					selectedGift := items.SelectItem("elixir_unseal", giftPool)
					result[i] = items.Item{
						ID:    selectedGift.ID,
						Name:  selectedGift.Name,
						Value: selectedGift.Value,
						Count: selectedGift.Count * count,
						Total: count,
					}
					totalValue += selectedGift.Value * selectedGift.Count * count

					h.logger.Debug("elixir_unseal", "[4.5] elixir %d (count 0) selected gift: %s (value:%d)", elixirIndex, selectedGift.Name, selectedGift.Value)
				}
			}
		}
	}

	h.logger.Debug("elixir_unseal", "[5] total value before jackpot check: %d, global jackpot: %d", totalValue, global.TotalJackpot)

	// 步骤5: 检查全局奖池限制，必要时降档
	if totalValue > global.TotalJackpot {
		h.logger.Debug("elixir_unseal", "[6] total value exceeds jackpot, need downgrade")
		result, totalValue = h.downgradeGifts(mapId, result, global.TotalJackpot, elixirCounts)
	}

	// 更新全局奖池
	global.TotalReturn += totalValue
	global.TotalJackpot -= totalValue
	users.UpdateGobalStats(global)

	h.logger.Debug("elixir_unseal", "[7] final total value: %d, remaining jackpot: %d", totalValue, global.TotalJackpot)

	return global.TotalJackpot, result
}

// downgradeGifts 降档机制：在同一出现次数内选择更低价值的礼物
func (h *Handler) downgradeGifts(mapId int64, result []items.Item, maxJackpot int64, elixirCounts map[int]int) ([]items.Item, int64) {
	h.logger.Debug("elixir_unseal", "[6.1] starting downgrade process, maxJackpot: %d", maxJackpot)

	mapGiftPool, exists := h.giftMap[mapId]
	if !exists {
		h.logger.Debug("elixir_unseal", "[6.1.1] no gift pool for map %d", mapId)
		return result, 0
	}

	maxIterations := 10 // 防止无限循环
	iteration := 0

	for iteration < maxIterations {
		iteration++

		// 计算当前总价值
		currentTotalValue := int64(0)
		for _, item := range result {
			currentTotalValue += item.Value * item.Count
		}

		// 检查是否已经满足奖池限制
		if currentTotalValue <= maxJackpot {
			h.logger.Debug("elixir_unseal", "[6.%d] target achieved, final value: %d", iteration, currentTotalValue)
			return result, currentTotalValue
		}

		needToSave := currentTotalValue - maxJackpot
		h.logger.Debug("elixir_unseal", "[6.%d] need to save: %d, current value: %d", iteration, needToSave, currentTotalValue)

		// 创建降档选项列表
		type DowngradeOption struct {
			Index       int        // 结果数组中的索引
			ElixirIndex int        // 仙丹索引
			CurrentGift items.Item // 当前礼物
			NewGift     items.Item // 降档后的礼物
			SavedValue  int64      // 节省的价值
		}

		var options []DowngradeOption

		// 为每个有礼物的位置寻找降档选项（只在同一出现次数内）
		for i, item := range result {
			if item.Value == 0 || item.Total == 0 {
				continue
			}

			elixirIndex := i % 8

			// 确定该位置对应的出现次数
			var currentCount int
			if gift, exists := elixirCounts[elixirIndex]; exists {
				// 仙丹出现过，使用其出现次数
				currentCount = gift
			} else {
				// 仙丹未出现，使用出现次数0
				currentCount = 0
			}

			// 只在当前出现次数的奖池中寻找更低价值的礼物
			currentPool, exists := mapGiftPool[currentCount]
			if !exists || len(currentPool) == 0 {
				continue
			}

			// 寻找同一奖池中价值更低的礼物
			for _, lowerGift := range currentPool {
				if lowerGift.Value < item.Value {
					currentValue := item.Value * item.Count
					newValue := lowerGift.Value * lowerGift.Count * item.Total
					savedValue := currentValue - newValue

					if savedValue > 0 {
						options = append(options, DowngradeOption{
							Index:       i,
							ElixirIndex: elixirIndex,
							CurrentGift: item,
							NewGift: items.Item{
								ID:    lowerGift.ID,
								Name:  lowerGift.Name,
								Value: lowerGift.Value,
								Count: lowerGift.Count * item.Total,
								Total: item.Total,
							},
							SavedValue: savedValue,
						})
					}
				}
			}
		}

		if len(options) == 0 {
			h.logger.Debug("elixir_unseal", "[6.%d] no more downgrade options available", iteration)
			break
		}

		h.logger.Debug("elixir_unseal", "[6.%d] found %d downgrade options", iteration, len(options))

		// 按节省价值从小到大排序，优先选择节省幅度最小的（更精确的降档）
		for i := 0; i < len(options)-1; i++ {
			for j := i + 1; j < len(options); j++ {
				if options[i].SavedValue > options[j].SavedValue {
					options[i], options[j] = options[j], options[i]
				}
			}
		}

		// 选择节省价值最小但足够的降档选项
		var selectedOption *DowngradeOption
		for _, option := range options {
			if option.SavedValue >= needToSave {
				selectedOption = &option
				break
			}
		}

		// 如果没有单个选项能满足需求，选择节省最多的
		if selectedOption == nil {
			// 重新按节省价值从大到小排序
			for i := 0; i < len(options)-1; i++ {
				for j := i + 1; j < len(options); j++ {
					if options[i].SavedValue < options[j].SavedValue {
						options[i], options[j] = options[j], options[i]
					}
				}
			}
			selectedOption = &options[0]
		}

		h.logger.Debug("elixir_unseal", "[6.%d] applying downgrade at index %d: %s -> %s, save: %d",
			iteration, selectedOption.Index, selectedOption.CurrentGift.Name, selectedOption.NewGift.Name,
			selectedOption.SavedValue)

		result[selectedOption.Index] = selectedOption.NewGift
	}

	// 最终计算总价值
	finalTotalValue := int64(0)
	for _, item := range result {
		finalTotalValue += item.Value * item.Count
	}

	h.logger.Debug("elixir_unseal", "[6.final] downgrade completed after %d iterations, final value: %d", iteration, finalTotalValue)

	return result, finalTotalValue
}

// NewModule 创建模块
func NewModule() core.APIModule {
	handler := NewHandler()

	return core.NewModuleBuilder[Request, net.Response]("elixir_unseal", "v1", net.MSG_TYPE_ELIXIR_UNSEAL).
		WithHandler(handler).
		Build()
}
