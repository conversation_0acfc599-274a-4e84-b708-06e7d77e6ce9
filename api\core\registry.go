package core

import (
	"fmt"
	"jackpot/filelog"
	"jackpot/net"
	"sync"
)

// DefaultModuleRegistry 默认模块注册器实现
type DefaultModuleRegistry struct {
	modules     map[byte]APIModule
	modulesList []APIModule
	mutex       sync.RWMutex
}

// NewDefaultModuleRegistry 创建新的模块注册器
func NewDefaultModuleRegistry() *DefaultModuleRegistry {
	return &DefaultModuleRegistry{
		modules:     make(map[byte]APIModule),
		modulesList: make([]APIModule, 0),
	}
}

// RegisterModule 注册模块
func (r *DefaultModuleRegistry) RegisterModule(module APIModule) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	msgType := module.GetMessageType()

	// 检查是否已经注册了相同消息类型的模块
	if existing, exists := r.modules[msgType]; exists {
		return fmt.Errorf("module for message type %d already registered: %s", msgType, existing.GetName())
	}

	r.modules[msgType] = module
	r.modulesList = append(r.modulesList, module)

	return nil
}

// GetModule 根据消息类型获取模块
func (r *DefaultModuleRegistry) GetModule(msgType byte) (APIModule, bool) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	module, exists := r.modules[msgType]
	return module, exists
}

// GetAllModules 获取所有模块
func (r *DefaultModuleRegistry) GetAllModules() []APIModule {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	// 返回副本以避免并发修改
	result := make([]APIModule, len(r.modulesList))
	copy(result, r.modulesList)
	return result
}

// InitAllModules 初始化所有模块
func (r *DefaultModuleRegistry) InitAllModules(container Container, config any) error {
	r.mutex.RLock()
	modules := make([]APIModule, len(r.modulesList))
	copy(modules, r.modulesList)
	r.mutex.RUnlock()

	logger := container.GetLogger()

	for _, module := range modules {
		if logger != nil {
			logger.Info("registry", "Initializing module: %s %s", module.GetName(), module.GetVersion())
		}

		if err := module.Init(container.Get("fileLogger").(*filelog.FileLogger), config); err != nil {
			if logger != nil {
				logger.Error("registry", "Failed to initialize module %s: %v", module.GetName(), err)
			}
			return fmt.Errorf("failed to initialize module %s: %w", module.GetName(), err)
		}

		if logger != nil {
			logger.Info("registry", "Successfully initialized module: %s", module.GetName())
		}
	}

	return nil
}

// GetModuleInfo 获取模块信息
func (r *DefaultModuleRegistry) GetModuleInfo() []ModuleInfo {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	info := make([]ModuleInfo, 0, len(r.modulesList))
	for _, module := range r.modulesList {
		info = append(info, ModuleInfo{
			Name:        module.GetName(),
			Version:     module.GetVersion(),
			MessageType: module.GetMessageType(),
		})
	}

	return info
}

// ProcessorRegistry 处理器注册器
type ProcessorRegistry struct {
	processors map[byte]func([]byte, chan net.RequestChannel)
	mutex      sync.RWMutex
}

// NewProcessorRegistry 创建处理器注册器
func NewProcessorRegistry() *ProcessorRegistry {
	return &ProcessorRegistry{
		processors: make(map[byte]func([]byte, chan net.RequestChannel)),
	}
}

// RegisterProcessor 注册处理器
func (r *ProcessorRegistry) RegisterProcessor(msgType byte, processor func([]byte, chan net.RequestChannel)) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	r.processors[msgType] = processor
}

// GetProcessor 获取处理器
func (r *ProcessorRegistry) GetProcessor(msgType byte) (func([]byte, chan net.RequestChannel), bool) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	processor, exists := r.processors[msgType]
	return processor, exists
}

// BuildProcessorRegistry 从模块注册器构建处理器注册器
func BuildProcessorRegistry(moduleRegistry ModuleRegistry) *ProcessorRegistry {
	processorRegistry := NewProcessorRegistry()

	for _, module := range moduleRegistry.GetAllModules() {
		processorRegistry.RegisterProcessor(module.GetMessageType(), module.Process)
	}

	return processorRegistry
}
