package core

import (
	"fmt"
	"jackpot/filelog"
	"sync"
)

// DIContainer 依赖注入容器实现
type DIContainer struct {
	dependencies map[string]any
	mutex        sync.RWMutex
	logger       Logger
}

// NewDIContainer 创建新的依赖注入容器
func NewDIContainer() *DIContainer {
	return &DIContainer{
		dependencies: make(map[string]any),
	}
}

// Register 注册依赖
func (c *DIContainer) Register(name string, instance any) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.dependencies[name] = instance

	// 如果注册的是logger，设置为默认logger
	if logger, ok := instance.(Logger); ok && name == "logger" {
		c.logger = logger
	}
}

// Get 获取依赖
func (c *DIContainer) Get(name string) any {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.dependencies[name]
}

// GetLogger 获取日志器
func (c *DIContainer) GetLogger() Logger {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.logger
}

// MustGet 获取依赖，如果不存在则panic
func (c *DIContainer) MustGet(name string) any {
	instance := c.Get(name)
	if instance == nil {
		panic(fmt.Sprintf("dependency '%s' not found", name))
	}
	return instance
}

// GetTyped 获取指定类型的依赖
func GetTyped[T any](c Container, name string) (T, error) {
	var zero T
	instance := c.Get(name)
	if instance == nil {
		return zero, fmt.Errorf("dependency '%s' not found", name)
	}

	typed, ok := instance.(T)
	if !ok {
		return zero, fmt.Errorf("dependency '%s' is not of expected type", name)
	}

	return typed, nil
}

// MustGetTyped 获取指定类型的依赖，如果不存在或类型不匹配则panic
func MustGetTyped[T any](c Container, name string) T {
	result, err := GetTyped[T](c, name)
	if err != nil {
		panic(err)
	}
	return result
}

// LoggerAdapter 文件日志器适配器
type LoggerAdapter struct {
	fileLogger *filelog.FileLogger
}

// NewLoggerAdapter 创建日志器适配器
func NewLoggerAdapter(fileLogger *filelog.FileLogger) *LoggerAdapter {
	return &LoggerAdapter{fileLogger: fileLogger}
}

// Debug 记录调试日志
func (l *LoggerAdapter) Debug(tag string, format string, args ...any) {
	if l.fileLogger != nil {
		l.fileLogger.Debug(tag, format, args...)
	}
}

// Info 记录信息日志
func (l *LoggerAdapter) Info(tag string, format string, args ...any) {
	if l.fileLogger != nil {
		l.fileLogger.Info(tag, format, args...)
	}
}

// Warn 记录警告日志
func (l *LoggerAdapter) Warn(tag string, format string, args ...any) {
	if l.fileLogger != nil {
		l.fileLogger.Warn(tag, format, args...)
	}
}

// Error 记录错误日志
func (l *LoggerAdapter) Error(tag string, format string, args ...any) {
	if l.fileLogger != nil {
		l.fileLogger.Error(tag, format, args...)
	}
}
