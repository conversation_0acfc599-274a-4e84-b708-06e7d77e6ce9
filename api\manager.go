package api

import (
	"fmt"
	"jackpot/api/core"
	"jackpot/api/elixir/drop"
	"jackpot/api/elixir/unseal"
	"jackpot/api/exchange"
	"jackpot/api/exchange/reroll"
	"jackpot/api/monster"
	"jackpot/api/neidan"
	"jackpot/api/stats"
	"jackpot/filelog"
	"jackpot/net"

	"gopkg.in/ini.v1"
)

// Manager API管理器
type Manager struct {
	container         core.Container
	moduleRegistry    core.ModuleRegistry
	processorRegistry *core.ProcessorRegistry
	logger            core.Logger
}

// NewManager 创建API管理器
func NewManager() *Manager {
	container := core.NewDIContainer()
	moduleRegistry := core.NewDefaultModuleRegistry()

	return &Manager{
		container:      container,
		moduleRegistry: moduleRegistry,
	}
}

// Initialize 初始化管理器
func (m *Manager) Initialize(logger *filelog.FileLogger, config *ini.File) error {
	// 注册依赖
	m.container.Register("fileLogger", logger)
	m.container.Register("config", config)
	m.container.Register("logger", core.NewLoggerAdapter(logger))

	m.logger = m.container.GetLogger()

	// 注册所有模块
	if err := m.registerModules(); err != nil {
		return fmt.Errorf("failed to register modules: %w", err)
	}

	// 初始化所有模块
	if err := m.moduleRegistry.InitAllModules(m.container, config); err != nil {
		return fmt.Errorf("failed to initialize modules: %w", err)
	}

	// 构建处理器注册器
	m.processorRegistry = core.BuildProcessorRegistry(m.moduleRegistry)

	m.logger.Info("manager", "API Manager initialized successfully")
	return nil
}

// registerModules 注册所有模块
func (m *Manager) registerModules() error {
	modules := []core.APIModule{
		stats.NewModule(),
		monster.NewModule(),
		neidan.NewModule(),
		exchange.NewModule(),
		reroll.NewModule(),
		drop.NewModule(),   // elixir
		unseal.NewModule(), // elixir
	}

	for _, module := range modules {
		if err := m.moduleRegistry.RegisterModule(module); err != nil {
			return fmt.Errorf("failed to register module %s: %w", module.GetName(), err)
		}
		m.logger.Info("manager", "Registered module: %s v%s (msgType: %d)", module.GetName(), module.GetVersion(), module.GetMessageType())
	}

	return nil
}

// ProcessMessage 处理消息
func (m *Manager) ProcessMessage(msgType byte, msgBody []byte, done chan net.RequestChannel) bool {
	processor, exists := m.processorRegistry.GetProcessor(msgType)
	if !exists {
		m.logger.Warn("manager", "No processor found for message type: %d", msgType)
		return false
	}

	// 异步处理消息
	go processor(msgBody, done)
	return true
}

// GetModuleInfo 获取模块信息
func (m *Manager) GetModuleInfo() []core.ModuleInfo {
	return m.moduleRegistry.GetModuleInfo()
}

// GetContainer 获取依赖注入容器
func (m *Manager) GetContainer() core.Container {
	return m.container
}

// GetModuleRegistry 获取模块注册器
func (m *Manager) GetModuleRegistry() core.ModuleRegistry {
	return m.moduleRegistry
}

// Shutdown 关闭管理器
func (m *Manager) Shutdown() {
	if m.logger != nil {
		m.logger.Info("manager", "API Manager shutting down")
	}
	// 在这里可以添加清理逻辑
}

// RegisterCustomModule 注册自定义模块
func (m *Manager) RegisterCustomModule(module core.APIModule) error {
	if err := m.moduleRegistry.RegisterModule(module); err != nil {
		return fmt.Errorf("failed to register custom module %s: %w", module.GetName(), err)
	}

	// 重新构建处理器注册器
	m.processorRegistry = core.BuildProcessorRegistry(m.moduleRegistry)

	m.logger.Info("manager", "Registered custom module: %s v%s", module.GetName(), module.GetVersion())
	return nil
}

// GetSupportedMessageTypes 获取支持的消息类型
func (m *Manager) GetSupportedMessageTypes() []byte {
	var messageTypes []byte
	for _, module := range m.moduleRegistry.GetAllModules() {
		messageTypes = append(messageTypes, module.GetMessageType())
	}
	return messageTypes
}

// IsMessageTypeSupported 检查是否支持指定的消息类型
func (m *Manager) IsMessageTypeSupported(msgType byte) bool {
	_, exists := m.processorRegistry.GetProcessor(msgType)
	return exists
}
