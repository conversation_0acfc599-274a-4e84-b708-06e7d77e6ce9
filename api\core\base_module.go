package core

import (
	"jackpot/filelog"
	"jackpot/net"
)

// BaseModule 基础模块实现
type BaseModule[TRequest any, TResponse any] struct {
	name        string
	version     string
	messageType byte
	processor   Processor[TRequest, TResponse]
	handler     RequestHandler[TRequest, TResponse]
	logger      Logger
}

// NewBaseModule 创建新的基础模块
func NewBaseModule[TRequest any, TResponse any](
	name string,
	version string,
	messageType byte,
	handler <PERSON><PERSON><PERSON><PERSON><PERSON>[TRequest, TResponse],
) *BaseModule[TRequest, TResponse] {
	processor := NewGenericProcessor[TRequest, TResponse](name)
	processor.SetHandler(handler)
	processor.SetResponseBuilder(NewDefaultResponseBuilder[TResponse]())

	return &BaseModule[TRequest, TResponse]{
		name:        name,
		version:     version,
		messageType: messageType,
		processor:   processor,
		handler:     handler,
	}
}

// GetName 返回模块名称
func (m *BaseModule[TRequest, TResponse]) GetName() string {
	return m.name
}

// GetVersion 返回模块版本
func (m *BaseModule[TRequest, TResponse]) GetVersion() string {
	return m.version
}

// GetMessageType 返回处理的消息类型
func (m *BaseModule[TRequest, TResponse]) GetMessageType() byte {
	return m.messageType
}

// Init 初始化模块
func (m *BaseModule[TRequest, TResponse]) Init(logger *filelog.FileLogger, config any) error {
	m.logger = NewLoggerAdapter(logger)
	m.processor.SetLogger(m.logger)

	// 如果handler实现了Initializer接口，则调用其Init方法
	if initializer, ok := m.handler.(Initializer); ok {
		return initializer.Init(logger, config)
	}

	return nil
}

// Process 处理消息
func (m *BaseModule[TRequest, TResponse]) Process(msgBody []byte, done chan net.RequestChannel) {
	m.processor.ProcessMessage(msgBody, done)
}

// SetCustomResponseBuilder 设置自定义响应构建器
func (m *BaseModule[TRequest, TResponse]) SetCustomResponseBuilder(builder ResponseBuilder[TResponse]) {
	m.processor.SetResponseBuilder(builder)
}

// Initializer 初始化器接口，用于需要自定义初始化逻辑的处理器
type Initializer interface {
	Init(logger *filelog.FileLogger, config any) error
}

// ValidatedRequestHandler 带验证的请求处理器基类
type ValidatedRequestHandler[TRequest any, TResponse any] struct {
	validator func(TRequest) error
	handler   func(TRequest) (int64, TResponse, error)
}

// NewValidatedRequestHandler 创建带验证的请求处理器
func NewValidatedRequestHandler[TRequest any, TResponse any](
	validator func(TRequest) error,
	handler func(TRequest) (int64, TResponse, error),
) *ValidatedRequestHandler[TRequest, TResponse] {
	return &ValidatedRequestHandler[TRequest, TResponse]{
		validator: validator,
		handler:   handler,
	}
}

// Validate 验证请求
func (h *ValidatedRequestHandler[TRequest, TResponse]) Validate(req TRequest) error {
	if h.validator != nil {
		return h.validator(req)
	}
	return nil
}

// Handle 处理请求
func (h *ValidatedRequestHandler[TRequest, TResponse]) Handle(req TRequest) (int64, TResponse, error) {
	return h.handler(req)
}

// SimpleRequestHandler 简单请求处理器，无验证
type SimpleRequestHandler[TRequest any, TResponse any] struct {
	handler func(TRequest) (int64, TResponse, error)
}

// NewSimpleRequestHandler 创建简单请求处理器
func NewSimpleRequestHandler[TRequest any, TResponse any](
	handler func(TRequest) (int64, TResponse, error),
) *SimpleRequestHandler[TRequest, TResponse] {
	return &SimpleRequestHandler[TRequest, TResponse]{
		handler: handler,
	}
}

// Validate 验证请求（简单处理器默认不验证）
func (h *SimpleRequestHandler[TRequest, TResponse]) Validate(req TRequest) error {
	return nil
}

// Handle 处理请求
func (h *SimpleRequestHandler[TRequest, TResponse]) Handle(req TRequest) (int64, TResponse, error) {
	return h.handler(req)
}

// ModuleBuilder 模块构建器
type ModuleBuilder[TRequest any, TResponse any] struct {
	name        string
	version     string
	messageType byte
	handler     RequestHandler[TRequest, TResponse]
	builder     ResponseBuilder[TResponse]
}

// NewModuleBuilder 创建模块构建器
func NewModuleBuilder[TRequest any, TResponse any](name string, version string, messageType byte) *ModuleBuilder[TRequest, TResponse] {
	return &ModuleBuilder[TRequest, TResponse]{
		name:        name,
		version:     version,
		messageType: messageType,
	}
}

// WithHandler 设置请求处理器
func (b *ModuleBuilder[TRequest, TResponse]) WithHandler(handler RequestHandler[TRequest, TResponse]) *ModuleBuilder[TRequest, TResponse] {
	b.handler = handler
	return b
}

// WithSimpleHandler 设置简单处理器
func (b *ModuleBuilder[TRequest, TResponse]) WithSimpleHandler(handler func(TRequest) (int64, TResponse, error)) *ModuleBuilder[TRequest, TResponse] {
	b.handler = NewSimpleRequestHandler(handler)
	return b
}

// WithValidatedHandler 设置带验证的处理器
func (b *ModuleBuilder[TRequest, TResponse]) WithValidatedHandler(
	validator func(TRequest) error,
	handler func(TRequest) (int64, TResponse, error),
) *ModuleBuilder[TRequest, TResponse] {
	b.handler = NewValidatedRequestHandler(validator, handler)
	return b
}

// WithResponseBuilder 设置响应构建器
func (b *ModuleBuilder[TRequest, TResponse]) WithResponseBuilder(builder ResponseBuilder[TResponse]) *ModuleBuilder[TRequest, TResponse] {
	b.builder = builder
	return b
}

// Build 构建模块
func (b *ModuleBuilder[TRequest, TResponse]) Build() APIModule {
	if b.handler == nil {
		panic("handler is required")
	}

	module := NewBaseModule(b.name, b.version, b.messageType, b.handler)

	if b.builder != nil {
		module.SetCustomResponseBuilder(b.builder)
	}

	return module
}
