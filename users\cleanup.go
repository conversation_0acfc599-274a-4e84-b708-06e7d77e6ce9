package users

import (
	"context"
	"fmt"
	"jackpot/db"
	"jackpot/env"
	"log/slog"
	"os"
	"path/filepath"
	"time"

	"github.com/redis/go-redis/v9"
)

var (
	backupDir string
)

func Cleanup() (int, error) {
	ctx := context.Background()

	// 需要保留的key模式列表
	keepPatterns := []string{
		fmt.Sprintf("JACKPOT_STATS:%s:v2:PLAT:*", env.PlatId), // 保留平台奖池
	}

	var totalDeleted int
	var cursor uint64
	const batchSize = 1000 // 每批处理1000个key

	// 使用SCAN迭代（比KEYS更安全）
	for {
		keys, cur, err := db.Rdb().Scan(ctx, cursor, "*", batchSize).Result()
		if err != nil {
			return 0, fmt.Errorf("scan error: %v", err)
		}

		// 过滤需要删除的key
		var toDelete []string
		for _, key := range keys {
			if !matchAnyPattern(key, keepPatterns) {
				toDelete = append(toDelete, key)
			}
		}

		// 批量删除
		if len(toDelete) > 0 {
			logger, err := newBackupLogger()
			if err != nil {
				return 0, fmt.Errorf("create backup logger error: %v", err)
			}

			getCmds, err := db.Rdb().Pipelined(ctx, func(pipe redis.Pipeliner) error {
				for _, key := range toDelete {
					pipe.Get(ctx, key)
				}
				return nil
			})
			if err != nil && err != redis.Nil {
				return 0, fmt.Errorf("pipeline get error: %v", err)
			}
			for _, cmd := range getCmds {
				if cmd.Err() != nil && cmd.Err() != redis.Nil {
					return 0, fmt.Errorf("get error: %v", cmd.Err())
				}
				logger.Info(cmd.String())
			}

			if _, err = db.Rdb().Pipelined(ctx, func(pipe redis.Pipeliner) error {
				pipe.Del(ctx, toDelete...)
				return nil
			}); err != nil {
				return 0, fmt.Errorf("pipeline del error: %v", err)
			}

			totalDeleted += len(toDelete)
		}

		// 更新游标
		if cur == 0 {
			break
		}
		cursor = cur
	}

	return totalDeleted, nil // 返回删除的key数量
}

func matchAnyPattern(key string, patterns []string) bool {
	for _, pattern := range patterns {
		if matched, _ := filepath.Match(pattern, key); matched {
			return true
		}
	}
	return false
}

func newBackupLogger() (*slog.Logger, error) {
	if err := os.MkdirAll(backupDir, 0755); err != nil {
		return nil, err
	}

	fileName := filepath.Join(backupDir, time.Now().Format("20060102-150405")+".bak")
	file, err := os.OpenFile(fileName, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return nil, err
	}
	logger := slog.New(slog.NewJSONHandler(file, nil))
	return logger, nil
}
