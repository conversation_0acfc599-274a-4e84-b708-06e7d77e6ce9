package core

import (
	"jackpot/filelog"
	"jackpot/net"
)

// APIModule 定义API模块的基础接口
type APIModule interface {
	// GetName 返回模块名称
	GetName() string
	// GetVersion 返回模块版本
	GetVersion() string
	// GetMessageType 返回处理的消息类型
	GetMessageType() byte
	// Init 初始化模块
	Init(logger *filelog.FileLogger, config any) error
	// Process 处理消息
	Process(msgBody []byte, done chan net.RequestChannel)
}

// RequestHandler 泛型请求处理器接口
type RequestHandler[TRequest any, TResponse any] interface {
	// Validate 验证请求
	Validate(req TRequest) error
	// Handle 处理请求
	Handle(req TRequest) (int64, TResponse, error)
}

// ResponseBuilder 响应构建器接口
type ResponseBuilder[TResponse any] interface {
	// BuildSuccess 构建成功响应
	BuildSuccess(data TResponse, jackpot int64) net.RequestChannel
	// BuildError 构建错误响应
	BuildError(code byte) net.RequestChannel
}

// Logger 日志接口
type Logger interface {
	Debug(tag string, format string, args ...any)
	Info(tag string, format string, args ...any)
	Warn(tag string, format string, args ...any)
	Error(tag string, format string, args ...any)
}

// Container 依赖注入容器接口
type Container interface {
	// Register 注册依赖
	Register(name string, instance any)
	// Get 获取依赖
	Get(name string) any
	// GetLogger 获取日志器
	GetLogger() Logger
}

// ModuleRegistry 模块注册器接口
type ModuleRegistry interface {
	// RegisterModule 注册模块
	RegisterModule(module APIModule) error
	// GetModule 根据消息类型获取模块
	GetModule(msgType byte) (APIModule, bool)
	// GetAllModules 获取所有模块
	GetAllModules() []APIModule
	// InitAllModules 初始化所有模块
	InitAllModules(container Container, config any) error
	// GetModuleInfo 获取模块信息
	GetModuleInfo() []ModuleInfo
}

// ModuleInfo 模块信息
type ModuleInfo struct {
	Name        string `json:"name"`
	Version     string `json:"version"`
	MessageType byte   `json:"messageType"`
}

// Processor 泛型消息处理器接口
type Processor[TRequest any, TResponse any] interface {
	// ProcessMessage 处理消息
	ProcessMessage(msgBody []byte, done chan net.RequestChannel)
	// SetHandler 设置请求处理器
	SetHandler(handler RequestHandler[TRequest, TResponse])
	// SetResponseBuilder 设置响应构建器
	SetResponseBuilder(builder ResponseBuilder[TResponse])
	// SetLogger 设置日志器
	SetLogger(logger Logger)
}
